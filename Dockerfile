# Use an official Deno runtime as a parent image
FROM denoland/deno:alpine

# Set the working directory in the container
WORKDIR /app

# Copy the Deno configuration files first to leverage Docker cache for dependencies
COPY deno.json deno.lock ./

# Copy the source code
# Adjust this if your application structure is different or you have other static assets
COPY src/ ./src/

# Cache dependencies using the entrypoint specified in your deno.json (src/app.ts)
# This step helps in faster startups and can leverage Docker's build cache.
RUN deno cache --config deno.json src/app.ts

# Expose the port the app runs on.
# This should match the port your Hono application listens on and the port in component.yaml.
EXPOSE 8000

# Define the command to run the application in production.
# This uses the entrypoint from your deno.json.
CMD ["run", "-A", "--unstable-kv", "--unstable-cron", "src/app.ts"]

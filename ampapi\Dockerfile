FROM denoland/deno:1.40.2

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY deno.json deno.lock* ./

# 缓存依赖
RUN deno cache --reload deno.json

# 复制源代码
COPY src/ ./src/
COPY scripts/ ./scripts/

# 创建日志目录
RUN mkdir -p logs

# 设置权限
RUN chown -R deno:deno /app

# 切换到非root用户
USER deno

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD deno run --allow-net --allow-env -q -c '
    try {
      const response = await fetch("http://localhost:8000/health");
      if (response.ok) {
        console.log("Health check passed");
        Deno.exit(0);
      } else {
        console.log("Health check failed:", response.status);
        Deno.exit(1);
      }
    } catch (error) {
      console.log("Health check error:", error.message);
      Deno.exit(1);
    }
  '

# 启动命令
CMD ["deno", "run", "-A", "src/app.ts"]

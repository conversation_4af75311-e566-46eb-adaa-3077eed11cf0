# AmpAPI 项目总结

## 🎯 项目概述

AmpAPI 是一个基于 Deno 和 Hono 框架构建的 API 代理服务，主要实现以下两个核心功能：

1. **Anthropic 到 OpenAI 格式转换** - 将 Anthropic Claude API 的请求/响应格式转换为 OpenAI 兼容格式
2. **API Key 自动轮询** - 智能管理多个 API Key，实现负载均衡和故障转移

## 🏗️ 技术架构

### 核心技术栈
- **运行时**: Deno 1.40+
- **Web 框架**: Hono 4.7+
- **数据库**: PostgreSQL (推荐 Neon)
- **类型验证**: Zod
- **容器化**: Docker + Docker Compose

### 项目结构
```
ampapi/
├── src/
│   ├── app.ts                    # 应用入口
│   ├── env.ts                    # 环境配置
│   ├── lib/
│   │   ├── create-app.ts         # 应用创建
│   │   ├── db.ts                 # 数据库操作
│   │   └── types.ts              # 类型定义
│   ├── utils/
│   │   └── format-converter.ts   # 格式转换工具
│   ├── router/
│   │   ├── chat/
│   │   │   └── completions.ts    # 聊天完成路由
│   │   └── api-keys.ts           # API Key 管理路由
│   └── middleware/
│       └── error-handler.ts      # 错误处理中间件
├── scripts/
│   ├── add-api-key.ts           # 添加 API Key 脚本
│   └── test-api.ts              # API 测试脚本
├── examples/
│   └── usage-examples.md        # 使用示例
├── deno.json                    # Deno 配置
├── Dockerfile                   # Docker 配置
├── docker-compose.yml           # Docker Compose 配置
└── README.md                    # 项目文档
```

## 🔧 核心功能实现

### 1. API Key 轮询机制

**策略**: 最少使用优先 (Least Recently Used)
- 从激活且未达到使用限制的 API Key 中选择
- 按使用次数升序排列，优先使用使用量最少的 Key
- 自动禁用达到限制或认证失败的 Key

**关键函数**:
```typescript
// 获取可用 API Key
export async function getAvailableApiKey(): Promise<ApiKey | null>

// 更新 API Key 状态
export async function updateApiKeyStatus(key: string, options: {...})
```

### 2. 格式转换机制

**请求转换**: OpenAI → Anthropic
- 提取系统消息到 `system` 字段
- 转换消息格式和模型名称
- 处理参数映射

**响应转换**: Anthropic → OpenAI
- 统一响应结构
- 转换停止原因
- 处理使用量统计

**流式转换**: 实时处理 SSE 数据流
- 解析 Anthropic 流式事件
- 转换为 OpenAI 兼容格式
- 保持流式传输性能

### 3. 错误处理和监控

**多层错误处理**:
- 全局错误捕获中间件
- API 级别错误处理
- 数据库操作错误处理

**监控功能**:
- 请求日志记录
- API Key 使用统计
- 响应时间监控
- 成功率统计

## 📊 数据库设计

### API Keys 表
```sql
CREATE TABLE api_keys (
  id SERIAL PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  usage INTEGER NOT NULL DEFAULT 0,
  usage_limit INTEGER NOT NULL DEFAULT 1000,
  active BOOLEAN NOT NULL DEFAULT true,
  disabled_at TIMESTAMP,
  disabled_reason TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### API Request Logs 表
```sql
CREATE TABLE api_request_logs (
  id SERIAL PRIMARY KEY,
  api_key TEXT NOT NULL,
  success BOOLEAN NOT NULL,
  error_type TEXT,
  error_message TEXT,
  request_time TIMESTAMP NOT NULL,
  response_time TIMESTAMP,
  request_id TEXT NOT NULL,
  response_time_ms INTEGER
);
```

## 🚀 部署和使用

### 快速启动
```bash
# 1. 配置环境变量
cp .env.example .env

# 2. 启动服务
deno task dev

# 3. 添加 API Key
deno task add-key sk-ant-api03-your-key-here 1000

# 4. 测试 API
deno task test
```

### Docker 部署
```bash
# 使用 Docker Compose
deno task docker:compose

# 查看日志
deno task docker:logs
```

## 🔌 API 接口

### 核心接口
- `POST /v1/chat/completions` - 聊天完成（兼容 OpenAI）
- `GET /api-keys` - 获取 API Key 列表
- `POST /api-keys` - 添加 API Key
- `GET /health` - 健康检查
- `GET /status` - 服务状态

### 兼容性
- 完全兼容 OpenAI Chat Completions API
- 支持流式和非流式请求
- 支持所有 OpenAI 客户端库

## 🎯 核心优势

1. **智能负载均衡**: 基于使用量的最优分配策略
2. **自动故障转移**: API Key 失效时自动切换
3. **完全兼容**: 无缝替换 OpenAI API
4. **高性能**: 基于 Deno 的高性能运行时
5. **易于部署**: 支持 Docker 一键部署
6. **完善监控**: 详细的请求日志和统计信息

## 🔮 扩展可能

### 短期扩展
- [ ] 添加更多 AI 提供商支持
- [ ] 实现 API Key 自动购买和注册
- [ ] 添加 Web 管理界面
- [ ] 支持更多认证方式

### 长期规划
- [ ] 支持多租户
- [ ] 添加缓存层
- [ ] 实现请求队列
- [ ] 支持插件系统

## 📝 开发说明

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 Deno 最佳实践
- 完整的类型定义
- 详细的错误处理

### 测试策略
- 单元测试覆盖核心逻辑
- 集成测试验证 API 功能
- 性能测试确保响应时间

### 安全考虑
- API Key 脱敏显示
- 请求速率限制
- 安全响应头
- 输入验证和清理

这个项目成功实现了参考项目的核心功能，并在此基础上进行了优化和扩展，提供了一个完整、可用的 API 代理解决方案。

# AmpAPI

一个基于 Deno 和 Hono 的 API 代理服务，提供以下核心功能：

1. **Anthropic 到 OpenAI 格式转换** - 将 Anthropic Claude API 请求/响应转换为 OpenAI 兼容格式
2. **API Key 自动轮询** - 智能管理多个 API Key，实现负载均衡和故障转移

## 功能特性

- 🔄 智能 API Key 轮询（基于使用量的负载均衡）
- 🔀 Anthropic ↔ OpenAI 格式转换
- 📊 完整的请求监控和日志记录
- 🛡️ 自动故障转移和错误处理
- 💾 PostgreSQL 数据持久化

## 快速开始

### 环境要求

- Deno 1.40+
- PostgreSQL 数据库（推荐使用 Neon）

### 安装和运行

1. 克隆项目
```bash
git clone <your-repo>
cd ampapi
```

2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要的配置
```

3. 运行项目
```bash
# 开发模式
deno task dev

# 生产模式
deno task start
```

## API 使用

### 聊天完成接口

```bash
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "claude-3-sonnet-20240229",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "stream": false
}
```

## 环境变量配置

| 变量名 | 描述 | 必需 |
|--------|------|------|
| `DATABASE_URL` | PostgreSQL 数据库连接字符串 | ✅ |
| `ANTHROPIC_BASE_URL` | Anthropic API 基础 URL | ✅ |
| `LOG_LEVEL` | 日志级别 (debug/info/warn/error) | ❌ |

## 项目结构

```
ampapi/
├── src/
│   ├── app.ts              # 应用入口
│   ├── env.ts              # 环境变量配置
│   ├── lib/
│   │   ├── db.ts           # 数据库操作
│   │   ├── types.ts        # 类型定义
│   │   └── create-app.ts   # 应用创建
│   ├── utils/
│   │   └── format-converter.ts  # 格式转换工具
│   ├── router/
│   │   └── chat/
│   │       └── completions.ts   # 聊天完成路由
│   └── middleware/
│       └── error-handler.ts     # 错误处理中间件
├── deno.json
└── README.md
```

## 许可证

MIT License

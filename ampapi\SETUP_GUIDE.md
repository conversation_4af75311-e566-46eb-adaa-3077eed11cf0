# AmpAPI 设置指南

## 📋 环境变量配置

根据您提供的信息，您需要配置以下环境变量：

### 必需的环境变量

1. **DATABASE_URL** - PostgreSQL 数据库连接字符串
2. **ANTHROPIC_BASE_URL** - 您的 Anthropic API 地址

### 可选的环境变量

3. **LOG_LEVEL** - 日志级别 (默认: info)
4. **PORT** - 服务端口 (默认: 8000)

## 🔧 配置步骤

### 方案一：使用 Docker Compose (推荐)

1. **确保 `.env` 文件配置正确**：
```bash
# 数据库配置 (使用 Docker Compose 中的 PostgreSQL)
DATABASE_URL=*************************************************/ampapi

# Anthropic API 配置
ANTHROPIC_BASE_URL=https://ampcode.com/api/provider/anthropic

# 日志配置
LOG_LEVEL=info

# 服务配置
PORT=8000
```

2. **启动服务**：
```bash
# 启动数据库和应用
docker-compose up -d

# 查看日志
docker-compose logs -f ampapi
```

3. **添加您的 API Keys**：
```bash
# 第一个 API Key
deno run -A scripts/add-api-key.ts "sgamp_user_01JYKEQEKQJHD2Z6FDEQAJ3ATD_2c64328445ff8689f19d0a3d19f2de028ad829a1dd561cded77510623b8d444f" 1000

# 第二个 API Key  
deno run -A scripts/add-api-key.ts "sgamp_user_01JYKA0SXFZK45893FY27NH4C0_9391589b1568a9c7d9ecd45bcfd0543e4b13aa6e9ab06f156563ca0e3029bf2c" 1000
```

### 方案二：使用云数据库

1. **获取数据库连接字符串**：
   - 推荐使用 [Neon](https://neon.tech) (免费)
   - 或者 [Supabase](https://supabase.com) (免费)
   - 或者其他 PostgreSQL 云服务

2. **更新 `.env` 文件**：
```bash
# 替换为您的实际数据库连接字符串
DATABASE_URL=********************************************/database

# Anthropic API 配置
ANTHROPIC_BASE_URL=https://ampcode.com/api/provider/anthropic

LOG_LEVEL=info
PORT=8000
```

3. **启动应用**：
```bash
deno task dev
```

4. **添加 API Keys**（同上）

### 方案三：本地 PostgreSQL

1. **安装 PostgreSQL**：
   - Windows: 下载 PostgreSQL 安装包
   - macOS: `brew install postgresql`
   - Linux: `sudo apt-get install postgresql`

2. **创建数据库**：
```sql
CREATE DATABASE ampapi;
CREATE USER ampapi WITH PASSWORD 'ampapi_password';
GRANT ALL PRIVILEGES ON DATABASE ampapi TO ampapi;
```

3. **更新 `.env` 文件**：
```bash
DATABASE_URL=postgresql://ampapi:ampapi_password@localhost:5432/ampapi
ANTHROPIC_BASE_URL=https://ampcode.com/api/provider/anthropic
LOG_LEVEL=info
PORT=8000
```

## 🚀 快速启动

### 自动设置 (推荐)

**Linux/macOS**:
```bash
chmod +x setup.sh
./setup.sh
```

**Windows**:
```cmd
setup.bat
```

### 手动设置

1. **启动数据库**：
```bash
docker-compose up -d postgres
```

2. **启动应用**：
```bash
deno task dev
```

3. **添加 API Keys**：
```bash
# 在新的终端窗口中执行
deno task add-key "sgamp_user_01JYKEQEKQJHD2Z6FDEQAJ3ATD_2c64328445ff8689f19d0a3d19f2de028ad829a1dd561cded77510623b8d444f" 1000
deno task add-key "sgamp_user_01JYKA0SXFZK45893FY27NH4C0_9391589b1568a9c7d9ecd45bcfd0543e4b13aa6e9ab06f156563ca0e3029bf2c" 1000
```

4. **测试 API**：
```bash
deno task test
```

## 🧪 验证设置

访问以下端点验证设置是否成功：

1. **健康检查**: http://localhost:8000/health
2. **服务状态**: http://localhost:8000/status  
3. **API Keys 列表**: http://localhost:8000/api-keys

## 📝 测试 API

```bash
# 测试聊天完成接口
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-haiku",
    "messages": [
      {
        "role": "user", 
        "content": "Hello! Please respond with just \"Test successful\" if you can see this message."
      }
    ],
    "max_tokens": 50,
    "stream": false
  }'
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `DATABASE_URL` 是否正确
   - 确保数据库服务正在运行
   - 检查网络连接

2. **API Key 认证失败**
   - 确认 API Key 格式正确
   - 检查 `ANTHROPIC_BASE_URL` 是否正确
   - 验证 API Key 是否有效

3. **端口被占用**
   - 修改 `.env` 文件中的 `PORT` 值
   - 或者停止占用端口的其他服务

### 查看日志

```bash
# Docker 方式
docker-compose logs -f ampapi

# 直接运行方式
# 日志会直接显示在终端
```

## 🎯 下一步

设置完成后，您可以：

1. 查看 `examples/usage-examples.md` 了解详细用法
2. 使用任何 OpenAI 兼容的客户端库
3. 监控 API 使用情况和统计信息
4. 根据需要添加更多 API Keys

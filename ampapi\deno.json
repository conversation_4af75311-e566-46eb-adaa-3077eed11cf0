{"imports": {"@neondatabase/serverless": "npm:@neondatabase/serverless@^1.0.0", "hono": "npm:hono@^4.7.10", "zod": "npm:zod@^3.25.28", "@/": "./src/"}, "tasks": {"dev": "deno run -A --watch src/app.ts", "start": "deno run -A src/app.ts", "test": "deno run -A scripts/test-api.ts", "add-key": "deno run -A scripts/add-api-key.ts", "docker:build": "docker build -t ampapi .", "docker:run": "docker run -p 8000:8000 --env-file .env ampapi", "docker:compose": "docker-compose up -d", "docker:logs": "docker-compose logs -f ampapi"}, "compilerOptions": {"jsx": "precompile", "jsxImportSource": "hono/jsx"}, "fmt": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve"}, "lint": {"rules": {"tags": ["recommended"]}}}
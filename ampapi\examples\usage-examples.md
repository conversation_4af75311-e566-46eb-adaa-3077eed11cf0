# AmpAPI 使用示例

## 基本设置

### 1. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
# DATABASE_URL=********************************************/database
# ANTHROPIC_BASE_URL=https://api.anthropic.com
# LOG_LEVEL=info
# PORT=8000
```

### 2. 启动服务

```bash
# 开发模式
deno task dev

# 生产模式
deno task start

# 使用 Docker
deno task docker:compose
```

### 3. 添加 API Key

```bash
# 使用脚本添加
deno task add-key sk-ant-api03-your-key-here 1000

# 或通过 API
curl -X POST http://localhost:8000/api-keys \
  -H "Content-Type: application/json" \
  -d '{"key": "sk-ant-api03-your-key-here", "usageLimit": 1000}'
```

## API 使用示例

### 1. 健康检查

```bash
curl http://localhost:8000/health
```

响应：
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

### 2. 服务状态

```bash
curl http://localhost:8000/status
```

### 3. 聊天完成（非流式）

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-haiku",
    "messages": [
      {
        "role": "user",
        "content": "Hello! How are you?"
      }
    ],
    "max_tokens": 100,
    "stream": false
  }'
```

### 4. 聊天完成（流式）

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-haiku",
    "messages": [
      {
        "role": "user",
        "content": "Count from 1 to 5"
      }
    ],
    "max_tokens": 100,
    "stream": true
  }'
```

### 5. 管理 API Keys

```bash
# 查看所有 API Keys
curl http://localhost:8000/api-keys

# 添加新的 API Key
curl -X POST http://localhost:8000/api-keys \
  -H "Content-Type: application/json" \
  -d '{"key": "sk-ant-api03-new-key", "usageLimit": 2000}'

# 重置 API Key 使用量
curl -X POST http://localhost:8000/api-keys/sk-ant-api03-your-key/reset

# 删除 API Key
curl -X DELETE http://localhost:8000/api-keys/sk-ant-api03-your-key
```

## 编程语言示例

### JavaScript/Node.js

```javascript
// 非流式请求
async function chatCompletion() {
  const response = await fetch('http://localhost:8000/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'claude-3-haiku',
      messages: [
        { role: 'user', content: 'Hello!' }
      ],
      max_tokens: 100,
      stream: false
    })
  });

  const data = await response.json();
  console.log(data.choices[0].message.content);
}

// 流式请求
async function streamingChat() {
  const response = await fetch('http://localhost:8000/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'claude-3-haiku',
      messages: [
        { role: 'user', content: 'Count from 1 to 5' }
      ],
      max_tokens: 100,
      stream: true
    })
  });

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    const chunk = decoder.decode(value);
    console.log(chunk);
  }
}
```

### Python

```python
import requests
import json

# 非流式请求
def chat_completion():
    response = requests.post(
        'http://localhost:8000/v1/chat/completions',
        headers={'Content-Type': 'application/json'},
        json={
            'model': 'claude-3-haiku',
            'messages': [
                {'role': 'user', 'content': 'Hello!'}
            ],
            'max_tokens': 100,
            'stream': False
        }
    )
    
    data = response.json()
    print(data['choices'][0]['message']['content'])

# 流式请求
def streaming_chat():
    response = requests.post(
        'http://localhost:8000/v1/chat/completions',
        headers={'Content-Type': 'application/json'},
        json={
            'model': 'claude-3-haiku',
            'messages': [
                {'role': 'user', 'content': 'Count from 1 to 5'}
            ],
            'max_tokens': 100,
            'stream': True
        },
        stream=True
    )
    
    for line in response.iter_lines():
        if line:
            print(line.decode('utf-8'))
```

## 测试和监控

### 运行测试

```bash
# 运行 API 测试
deno task test

# 测试特定服务器
deno task test http://your-server:8000
```

### 监控指标

```bash
# 获取统计信息
curl http://localhost:8000/api-keys/stats

# 获取不同时间范围的统计
curl "http://localhost:8000/api-keys/stats?range=1h"
curl "http://localhost:8000/api-keys/stats?range=24h"
curl "http://localhost:8000/api-keys/stats?range=7d"
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `DATABASE_URL` 环境变量
   - 确保数据库服务正在运行

2. **API Key 认证失败**
   - 检查 Anthropic API Key 是否有效
   - 确认 API Key 没有达到使用限制

3. **请求超时**
   - 检查网络连接
   - 调整超时设置

### 日志查看

```bash
# Docker 日志
deno task docker:logs

# 或直接查看
docker-compose logs -f ampapi
```

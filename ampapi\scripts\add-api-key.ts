#!/usr/bin/env -S deno run -A

/**
 * 添加 API Key 的命令行工具
 * 使用方法: deno run -A scripts/add-api-key.ts <api-key> [usage-limit]
 */

import { parseEnv } from "../src/env.ts";
import { initDatabase, addApiKey } from "../src/lib/db.ts";

async function main() {
  const args = Deno.args;
  
  if (args.length < 1) {
    console.error("❌ 使用方法: deno run -A scripts/add-api-key.ts <api-key> [usage-limit]");
    console.error("   例如: deno run -A scripts/add-api-key.ts sk-ant-api03-xxx 1000");
    Deno.exit(1);
  }

  const apiKey = args[0];
  const usageLimit = args[1] ? parseInt(args[1]) : 1000;

  if (isNaN(usageLimit) || usageLimit <= 0) {
    console.error("❌ 使用限制必须是正整数");
    Deno.exit(1);
  }

  try {
    // 初始化环境和数据库
    parseEnv();
    await initDatabase();

    // 添加 API Key
    await addApiKey(apiKey, usageLimit);
    
    console.log("✅ API Key 添加成功!");
    console.log(`   Key: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);
    console.log(`   使用限制: ${usageLimit}`);
    
  } catch (error) {
    console.error("❌ 添加 API Key 失败:", error);
    Deno.exit(1);
  }
}

if (import.meta.main) {
  await main();
}

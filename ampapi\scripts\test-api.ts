#!/usr/bin/env -S deno run -A

/**
 * API 测试脚本
 * 使用方法: deno run -A scripts/test-api.ts [server-url]
 */

async function testAPI(baseUrl: string = "http://localhost:8000") {
  console.log(`🧪 测试 AmpAPI 服务: ${baseUrl}`);
  console.log("");

  // 测试健康检查
  console.log("1. 测试健康检查...");
  try {
    const response = await fetch(`${baseUrl}/health`);
    const data = await response.json();
    console.log("✅ 健康检查通过:", data);
  } catch (error) {
    console.error("❌ 健康检查失败:", error);
    return;
  }

  // 测试服务状态
  console.log("\n2. 测试服务状态...");
  try {
    const response = await fetch(`${baseUrl}/status`);
    const data = await response.json();
    console.log("✅ 服务状态:", data);
  } catch (error) {
    console.error("❌ 服务状态检查失败:", error);
  }

  // 测试 API Keys 列表
  console.log("\n3. 测试 API Keys 列表...");
  try {
    const response = await fetch(`${baseUrl}/api-keys`);
    const data = await response.json();
    console.log("✅ API Keys:", data);
  } catch (error) {
    console.error("❌ API Keys 列表获取失败:", error);
  }

  // 测试聊天完成接口（需要有可用的 API Key）
  console.log("\n4. 测试聊天完成接口...");
  try {
    const chatRequest = {
      model: "claude-3-haiku",
      messages: [
        {
          role: "user",
          content: "Hello! Please respond with just 'Test successful' if you can see this message."
        }
      ],
      max_tokens: 50,
      stream: false
    };

    const response = await fetch(`${baseUrl}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(chatRequest),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log("✅ 聊天完成接口测试成功:");
      console.log("   响应:", data.choices?.[0]?.message?.content || "无内容");
    } else {
      console.log("⚠️ 聊天完成接口返回错误:", data);
    }
  } catch (error) {
    console.error("❌ 聊天完成接口测试失败:", error);
  }

  // 测试流式聊天
  console.log("\n5. 测试流式聊天接口...");
  try {
    const streamRequest = {
      model: "claude-3-haiku",
      messages: [
        {
          role: "user",
          content: "Count from 1 to 3, each number on a new line."
        }
      ],
      max_tokens: 50,
      stream: true
    };

    const response = await fetch(`${baseUrl}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(streamRequest),
    });

    if (response.ok && response.body) {
      console.log("✅ 流式聊天接口连接成功，接收数据:");
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let chunks = 0;

      try {
        while (chunks < 10) { // 限制只读取前10个chunk
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value);
          console.log(`   Chunk ${++chunks}:`, chunk.substring(0, 100) + (chunk.length > 100 ? "..." : ""));
        }
      } finally {
        reader.releaseLock();
      }
    } else {
      const data = await response.json();
      console.log("⚠️ 流式聊天接口返回错误:", data);
    }
  } catch (error) {
    console.error("❌ 流式聊天接口测试失败:", error);
  }

  console.log("\n🎯 测试完成!");
}

async function main() {
  const serverUrl = Deno.args[0] || "http://localhost:8000";
  await testAPI(serverUrl);
}

if (import.meta.main) {
  await main();
}

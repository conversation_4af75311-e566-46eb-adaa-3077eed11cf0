#!/bin/bash

echo "🚀 设置 AmpAPI 项目..."

# 检查是否安装了 Deno
if ! command -v deno &> /dev/null; then
    echo "❌ Deno 未安装，请先安装 Deno: https://deno.land/manual/getting_started/installation"
    exit 1
fi

echo "✅ Deno 已安装"

# 启动数据库 (使用 Docker Compose)
echo "🐳 启动 PostgreSQL 数据库..."
docker-compose up -d postgres

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 启动应用 (这会自动初始化数据库表)
echo "🚀 启动 AmpAPI 服务..."
deno task dev &
APP_PID=$!

# 等待应用启动
echo "⏳ 等待应用启动..."
sleep 5

# 添加您的 API Keys
echo "🔑 添加 API Keys..."

# 第一个 API Key
echo "添加第一个 API Key..."
deno run -A scripts/add-api-key.ts "sgamp_user_01JYKEQEKQJHD2Z6FDEQAJ3ATD_2c64328445ff8689f19d0a3d19f2de028ad829a1dd561cded77510623b8d444f" 1000

# 第二个 API Key
echo "添加第二个 API Key..."
deno run -A scripts/add-api-key.ts "sgamp_user_01JYKA0SXFZK45893FY27NH4C0_9391589b1568a9c7d9ecd45bcfd0543e4b13aa6e9ab06f156563ca0e3029bf2c" 1000

echo "✅ API Keys 添加完成！"

# 测试 API
echo "🧪 测试 API..."
sleep 2
deno run -A scripts/test-api.ts

echo "🎉 设置完成！"
echo "📡 服务地址: http://localhost:8000"
echo "📚 API 文档: 查看 examples/usage-examples.md"

# 保持应用运行
wait $APP_PID

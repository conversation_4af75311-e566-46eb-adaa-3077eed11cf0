import { createApp } from "./lib/create-app.ts";
import { initDatabase } from "./lib/db.ts";
import { env } from "./env.ts";

// 导入路由
import chatCompletionsRouter from "./router/chat/completions.ts";
import apiKeysRouter from "./router/api-keys.ts";

// 导入中间件
import {
  errorHandler,
  notFoundHandler,
  timeoutHandler,
  bodySizeLimit,
  rateLimit,
  securityHeaders,
  skipHealthCheckLogs,
} from "./middleware/error-handler.ts";

async function startServer() {
  console.log("🚀 启动 AmpAPI 服务...");

  try {
    // 初始化数据库
    await initDatabase();

    // 创建应用实例
    const app = createApp();

    // 添加全局中间件
    app.use("*", securityHeaders);
    app.use("*", skipHealthCheckLogs());
    app.use("*", timeoutHandler(30000)); // 30秒超时
    app.use("*", bodySizeLimit(2 * 1024 * 1024)); // 2MB 限制
    app.use("*", rateLimit(120)); // 每分钟120个请求

    // 注册路由
    app.route("/", chatCompletionsRouter);
    app.route("/", apiKeysRouter);

    // API Keys 管理端点（简化版）
    app.get("/api-keys", async (c) => {
      const { getApiKeys, getTotalRemainingUsage } = await import("./lib/db.ts");
      
      try {
        const apiKeys = await getApiKeys();
        const totalRemaining = await getTotalRemainingUsage();
        
        return c.json({
          success: true,
          data: {
            totalKeys: apiKeys.length,
            activeKeys: apiKeys.filter(k => k.active).length,
            totalRemaining,
            keys: apiKeys.map(key => ({
              id: key.id,
              key: `${key.key.substring(0, 8)}...${key.key.substring(key.key.length - 4)}`,
              usage: key.usage,
              usageLimit: key.usageLimit,
              active: key.active,
              disabledReason: key.disabledReason,
            })),
          },
        });
      } catch (error) {
        console.error("获取 API Keys 失败:", error);
        return c.json({ error: "Failed to fetch API keys" }, 500);
      }
    });

    // 添加 API Key 端点
    app.post("/api-keys", async (c) => {
      const { addApiKey } = await import("./lib/db.ts");
      
      try {
        const body = await c.req.json();
        const { key, usageLimit = 1000 } = body;

        if (!key) {
          return c.json({ error: "Missing API key" }, 400);
        }

        await addApiKey(key, usageLimit);
        return c.json({ success: true, message: "API key added successfully" });
      } catch (error) {
        console.error("添加 API Key 失败:", error);
        return c.json({ error: "Failed to add API key" }, 500);
      }
    });

    // 服务状态端点
    app.get("/status", async (c) => {
      const { getTotalRemainingUsage, getApiRequestStats } = await import("./lib/db.ts");
      
      try {
        const totalRemaining = await getTotalRemainingUsage();
        const stats = await getApiRequestStats("24h");
        
        return c.json({
          status: "healthy",
          timestamp: new Date().toISOString(),
          version: "1.0.0",
          database: "connected",
          apiKeys: {
            totalRemainingUsage: totalRemaining,
          },
          stats: {
            last24h: {
              totalRequests: Number(stats.total_requests || 0),
              successfulRequests: Number(stats.successful_requests || 0),
              failedRequests: Number(stats.failed_requests || 0),
              successRate: stats.total_requests > 0 
                ? Number(((stats.successful_requests / stats.total_requests) * 100).toFixed(2))
                : 0,
            },
          },
        });
      } catch (error) {
        console.error("获取状态信息失败:", error);
        return c.json({
          status: "degraded",
          timestamp: new Date().toISOString(),
          error: "Failed to fetch status",
        }, 500);
      }
    });

    // 错误处理中间件（必须在最后添加）
    app.use("*", errorHandler);
    app.notFound(notFoundHandler);

    // 启动服务器
    console.log(`✅ 服务器启动成功！`);
    console.log(`📡 监听端口: ${env.PORT}`);
    console.log(`🌐 服务地址: http://localhost:${env.PORT}`);
    console.log(`📚 API 文档:`);
    console.log(`   - 健康检查: GET /health`);
    console.log(`   - 服务状态: GET /status`);
    console.log(`   - 聊天完成: POST /v1/chat/completions`);
    console.log(`   - API Keys: GET /api-keys`);
    console.log(`   - 添加 Key: POST /api-keys`);
    console.log("");

    Deno.serve({
      port: env.PORT,
      onListen: ({ port, hostname }) => {
        console.log(`🎯 服务器正在监听 http://${hostname}:${port}`);
      },
    }, app.fetch);

  } catch (error) {
    console.error("❌ 服务器启动失败:", error);
    Deno.exit(1);
  }
}

// 优雅关闭处理
function setupGracefulShutdown() {
  const signals = ["SIGINT", "SIGTERM"] as const;
  
  signals.forEach((signal) => {
    Deno.addSignalListener(signal, () => {
      console.log(`\n📴 收到 ${signal} 信号，正在优雅关闭服务器...`);
      
      // 这里可以添加清理逻辑，比如关闭数据库连接等
      console.log("✅ 服务器已关闭");
      Deno.exit(0);
    });
  });
}

// 启动应用
if (import.meta.main) {
  setupGracefulShutdown();
  await startServer();
}

import { z } from "zod";

const EnvSchema = z.object({
  // 数据库配置
  DATABASE_URL: z.string().min(1, "DATABASE_URL is required"),

  // Anthropic API 配置
  ANTHROPIC_BASE_URL: z.string().url("ANTHROPIC_BASE_URL must be a valid URL").default("https://api.anthropic.com"),

  // 日志配置
  LOG_LEVEL: z.enum(["debug", "info", "warn", "error"]).default("info"),

  // 服务配置
  PORT: z.string().transform(Number).pipe(z.number().int().min(1).max(65535)).default("8000"),
});

export type Environment = z.infer<typeof EnvSchema>;

export function parseEnv(): Environment {
  const envData = {
    DATABASE_URL: Deno.env.get("DATABASE_URL"),
    ANTHROPIC_BASE_URL: Deno.env.get("ANTHROPIC_BASE_URL"),
    LOG_LEVEL: Deno.env.get("LOG_LEVEL"),
    PORT: Deno.env.get("PORT"),
  };

  const result = EnvSchema.safeParse(envData);

  if (!result.success) {
    console.error("❌ 环境变量配置错误:");
    result.error.errors.forEach((error) => {
      console.error(`  - ${error.path.join(".")}: ${error.message}`);
    });
    console.error("\n当前环境变量:");
    console.error(`  DATABASE_URL: ${envData.DATABASE_URL || "未设置"}`);
    console.error(`  ANTHROPIC_BASE_URL: ${envData.ANTHROPIC_BASE_URL || "未设置"}`);
    console.error(`  LOG_LEVEL: ${envData.LOG_LEVEL || "未设置"}`);
    console.error(`  PORT: ${envData.PORT || "未设置"}`);
    Deno.exit(1);
  }

  return result.data;
}

// 全局环境变量实例
export const env = parseEnv();

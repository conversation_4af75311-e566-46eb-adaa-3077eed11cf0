import { Hono } from "hono";
import { logger } from "hono/logger";
import { cors } from "hono/cors";
import { AppContext } from "./types.ts";
import { env } from "../env.ts";

export function createApp() {
  const app = new Hono<{ Bindings: any; Variables: AppContext }>();

  // 添加请求ID中间件
  app.use("*", async (c, next) => {
    const requestId = crypto.randomUUID();
    c.set("requestId", requestId);
    c.header("X-Request-ID", requestId);
    await next();
  });

  // 日志中间件
  if (env.LOG_LEVEL === "debug" || env.LOG_LEVEL === "info") {
    app.use("*", logger());
  }

  // CORS 中间件
  app.use("*", cors({
    origin: "*",
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization", "X-Request-ID"],
  }));

  // 健康检查端点
  app.get("/health", (c) => {
    return c.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
    });
  });

  // 根路径
  app.get("/", (c) => {
    return c.json({
      message: "AmpAPI - Anthropic to OpenAI Proxy",
      version: "1.0.0",
      endpoints: {
        health: "/health",
        chat: "/v1/chat/completions",
        apiKeys: "/api-keys",
      },
    });
  });

  return app;
}

export function createRouter(basePath: string) {
  return new Hono().basePath(basePath);
}

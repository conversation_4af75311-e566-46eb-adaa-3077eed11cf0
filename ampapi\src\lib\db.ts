import { neon } from "@neondatabase/serverless";
import { env } from "../env.ts";
import { <PERSON><PERSON><PERSON><PERSON>, ApiKeyDisabledReason, ApiRequestLog } from "./types.ts";

const sql = neon(env.DATABASE_URL);

// 初始化数据库表
export async function initDatabase() {
  console.log("🔧 初始化数据库表...");

  // 创建 API Keys 表
  await sql`
    CREATE TABLE IF NOT EXISTS api_keys (
      id SERIAL PRIMARY KEY,
      key TEXT NOT NULL UNIQUE,
      usage INTEGER NOT NULL DEFAULT 0,
      usage_limit INTEGER NOT NULL DEFAULT 1000,
      active BOOLEAN NOT NULL DEFAULT true,
      disabled_at TIMESTAMP,
      disabled_reason TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // 创建 API 请求日志表
  await sql`
    CREATE TABLE IF NOT EXISTS api_request_logs (
      id SERIAL PRIMARY KEY,
      api_key TEXT NOT NULL,
      success BOOLEAN NOT NULL,
      error_type TEXT,
      error_message TEXT,
      request_time TIMESTAMP NOT NULL,
      response_time TIMESTAMP,
      request_id TEXT NOT NULL,
      response_time_ms INTEGER
    );
  `;

  // 创建索引以提高查询性能
  await sql`
    CREATE INDEX IF NOT EXISTS idx_api_keys_active_usage 
    ON api_keys (active, usage) WHERE active = true;
  `;

  await sql`
    CREATE INDEX IF NOT EXISTS idx_api_request_logs_api_key 
    ON api_request_logs (api_key);
  `;

  await sql`
    CREATE INDEX IF NOT EXISTS idx_api_request_logs_request_time 
    ON api_request_logs (request_time);
  `;

  console.log("✅ 数据库表初始化完成");
}

// API Key 管理函数

/**
 * 获取所有 API Keys
 */
export async function getApiKeys(): Promise<ApiKey[]> {
  const result = await sql`
    SELECT * FROM api_keys 
    ORDER BY created_at DESC
  `;
  return result as ApiKey[];
}

/**
 * 根据 key 获取单个 API Key
 */
export async function getApiKey(key: string): Promise<ApiKey | null> {
  const result = await sql`
    SELECT * FROM api_keys 
    WHERE key = ${key}
  `;
  return result.length > 0 ? result[0] as ApiKey : null;
}

/**
 * 添加新的 API Key
 */
export async function addApiKey(key: string, usageLimit: number = 1000): Promise<void> {
  await sql`
    INSERT INTO api_keys (key, usage_limit) 
    VALUES (${key}, ${usageLimit})
    ON CONFLICT (key) DO NOTHING
  `;
}

/**
 * 获取可用的 API Key（核心轮询逻辑）
 * 策略：选择激活状态且未达到使用限制的 Key，按使用量升序排列
 */
export async function getAvailableApiKey(): Promise<ApiKey | null> {
  const result = await sql`
    SELECT * FROM api_keys 
    WHERE active = true AND usage < usage_limit 
    ORDER BY usage ASC, created_at ASC 
    LIMIT 1
  `;
  return result.length > 0 ? result[0] as ApiKey : null;
}

/**
 * 更新 API Key 状态和使用量
 */
export async function updateApiKeyStatus(
  key: string,
  options: {
    incrementUsage?: boolean;
    disable?: boolean;
    disabledReason?: ApiKeyDisabledReason;
  } = {}
): Promise<void> {
  const { incrementUsage = false, disable = false, disabledReason } = options;

  if (disable && disabledReason) {
    // 禁用 API Key
    await sql`
      UPDATE api_keys 
      SET 
        active = false,
        disabled_at = CURRENT_TIMESTAMP,
        disabled_reason = ${disabledReason},
        updated_at = CURRENT_TIMESTAMP
      WHERE key = ${key}
    `;
  } else if (incrementUsage) {
    // 增加使用量，如果达到限制则自动禁用
    await sql`
      UPDATE api_keys 
      SET 
        usage = usage + 1,
        active = CASE 
          WHEN usage + 1 >= usage_limit THEN false 
          ELSE active 
        END,
        disabled_at = CASE 
          WHEN usage + 1 >= usage_limit THEN CURRENT_TIMESTAMP 
          ELSE disabled_at 
        END,
        disabled_reason = CASE 
          WHEN usage + 1 >= usage_limit THEN ${ApiKeyDisabledReason.USAGE_LIMIT_EXCEEDED}
          ELSE disabled_reason 
        END,
        updated_at = CURRENT_TIMESTAMP
      WHERE key = ${key} AND active = true
    `;
  }
}

/**
 * 获取总剩余使用量
 */
export async function getTotalRemainingUsage(): Promise<number> {
  const result = await sql`
    SELECT COALESCE(SUM(usage_limit - usage), 0) as total_remaining
    FROM api_keys 
    WHERE active = true
  `;
  return Number(result[0]?.total_remaining || 0);
}

/**
 * 重置 API Key 使用量
 */
export async function resetApiKeyUsage(key: string): Promise<void> {
  await sql`
    UPDATE api_keys 
    SET 
      usage = 0,
      active = true,
      disabled_at = NULL,
      disabled_reason = NULL,
      updated_at = CURRENT_TIMESTAMP
    WHERE key = ${key}
  `;
}

/**
 * 删除 API Key
 */
export async function deleteApiKey(key: string): Promise<void> {
  await sql`DELETE FROM api_keys WHERE key = ${key}`;
}

// API 请求日志函数

/**
 * 记录 API 请求
 */
export async function recordApiRequest(
  apiKey: string,
  success: boolean,
  requestId: string,
  options: {
    errorType?: string;
    errorMessage?: string;
    responseTimeMs?: number;
  } = {}
): Promise<void> {
  const { errorType, errorMessage, responseTimeMs } = options;
  
  await sql`
    INSERT INTO api_request_logs (
      api_key, 
      success, 
      error_type, 
      error_message, 
      request_time, 
      response_time, 
      request_id, 
      response_time_ms
    ) VALUES (
      ${apiKey}, 
      ${success}, 
      ${errorType || null}, 
      ${errorMessage || null}, 
      CURRENT_TIMESTAMP, 
      ${success ? sql`CURRENT_TIMESTAMP` : null}, 
      ${requestId}, 
      ${responseTimeMs || null}
    )
  `;
}

/**
 * 获取 API 请求统计
 */
export async function getApiRequestStats(timeRange: "1h" | "24h" | "7d" = "24h") {
  const interval = timeRange === "1h" ? "1 hour" : timeRange === "24h" ? "24 hours" : "7 days";
  
  const result = await sql`
    SELECT 
      COUNT(*) as total_requests,
      COUNT(*) FILTER (WHERE success = true) as successful_requests,
      COUNT(*) FILTER (WHERE success = false) as failed_requests,
      AVG(response_time_ms) FILTER (WHERE success = true) as avg_response_time_ms
    FROM api_request_logs 
    WHERE request_time >= CURRENT_TIMESTAMP - INTERVAL '${sql.unsafe(interval)}'
  `;
  
  return result[0];
}

import { Context } from "hono";

// API Key 相关类型
export interface ApiKey {
  id: number;
  key: string;
  usage: number;
  usageLimit: number;
  active: boolean;
  disabledAt: Date | null;
  disabledReason: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// API Key 禁用原因枚举
export enum ApiKeyDisabledReason {
  USAGE_LIMIT_EXCEEDED = "usage_limit_exceeded",
  AUTHENTICATION_ERROR = "authentication_error", 
  MANUALLY_DISABLED = "manually_disabled",
  RATE_LIMIT_EXCEEDED = "rate_limit_exceeded",
  EXPIRED = "expired",
  SECURITY_VIOLATION = "security_violation",
}

// API 请求记录
export interface ApiRequestLog {
  id: number;
  apiKey: string;
  success: boolean;
  errorType: string | null;
  errorMessage: string | null;
  requestTime: Date;
  responseTime: Date | null;
  requestId: string;
  responseTimeMs: number | null;
}

// OpenAI 兼容的消息格式
export interface OpenAIMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

// OpenAI 兼容的请求格式
export interface OpenAIRequest {
  model: string;
  messages: OpenAIMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  stream?: boolean;
  stop?: string | string[];
}

// OpenAI 兼容的响应格式
export interface OpenAIResponse {
  id: string;
  object: "chat.completion";
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: "assistant";
      content: string;
    };
    finish_reason: "stop" | "length" | "content_filter" | null;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// OpenAI 流式响应格式
export interface OpenAIStreamChunk {
  id: string;
  object: "chat.completion.chunk";
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: {
      role?: "assistant";
      content?: string;
    };
    finish_reason: "stop" | "length" | "content_filter" | null;
  }>;
}

// Anthropic 消息格式
export interface AnthropicMessage {
  role: "user" | "assistant";
  content: string;
}

// Anthropic 请求格式
export interface AnthropicRequest {
  model: string;
  max_tokens: number;
  messages: AnthropicMessage[];
  temperature?: number;
  top_p?: number;
  stream?: boolean;
  stop_sequences?: string[];
  system?: string;
}

// Anthropic 响应格式
export interface AnthropicResponse {
  id: string;
  type: "message";
  role: "assistant";
  content: Array<{
    type: "text";
    text: string;
  }>;
  model: string;
  stop_reason: "end_turn" | "max_tokens" | "stop_sequence" | null;
  stop_sequence: string | null;
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
}

// 应用上下文类型
export interface AppContext extends Context {
  get: (key: string) => any;
  set: (key: string, value: any) => void;
}

// 错误响应格式
export interface ErrorResponse {
  error: {
    message: string;
    type?: string;
    code?: string;
  };
}

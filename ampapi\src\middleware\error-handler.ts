import { Context, Next } from "hono";
import { HTTPException } from "hono/http-exception";
import { createOpenAIErrorResponse } from "../utils/format-converter.ts";
import { env } from "../env.ts";

/**
 * 全局错误处理中间件
 */
export async function errorHandler(c: Context, next: Next) {
  try {
    await next();
  } catch (error) {
    console.error("🚨 未处理的错误:", error);

    // 如果是 HTTP 异常，直接返回
    if (error instanceof HTTPException) {
      return error.getResponse();
    }

    // 记录错误详情（仅在开发环境）
    if (env.LOG_LEVEL === "debug") {
      console.error("错误堆栈:", error instanceof Error ? error.stack : error);
    }

    // 返回通用错误响应
    return c.json(
      createOpenAIErrorResponse(
        "Internal server error",
        "internal_error"
      ),
      500
    );
  }
}

/**
 * 404 处理中间件
 */
export function notFoundHandler(c: Context) {
  return c.json(
    createOpenAIErrorResponse(
      `Endpoint not found: ${c.req.method} ${c.req.path}`,
      "not_found"
    ),
    404
  );
}

/**
 * 请求超时处理
 */
export function timeoutHandler(timeoutMs: number = 30000) {
  return async (c: Context, next: Next) => {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error("Request timeout"));
      }, timeoutMs);
    });

    try {
      await Promise.race([next(), timeoutPromise]);
    } catch (error) {
      if (error instanceof Error && error.message === "Request timeout") {
        return c.json(
          createOpenAIErrorResponse(
            "Request timeout",
            "timeout"
          ),
          408
        );
      }
      throw error;
    }
  };
}

/**
 * 请求大小限制中间件
 */
export function bodySizeLimit(maxSizeBytes: number = 1024 * 1024) { // 默认 1MB
  return async (c: Context, next: Next) => {
    const contentLength = c.req.header("content-length");
    
    if (contentLength && parseInt(contentLength) > maxSizeBytes) {
      return c.json(
        createOpenAIErrorResponse(
          "Request body too large",
          "payload_too_large"
        ),
        413
      );
    }

    await next();
  };
}

/**
 * 速率限制中间件（简单实现）
 */
export function rateLimit(requestsPerMinute: number = 60) {
  const requests = new Map<string, number[]>();

  return async (c: Context, next: Next) => {
    const clientIP = c.req.header("x-forwarded-for") || 
                     c.req.header("x-real-ip") || 
                     "unknown";
    
    const now = Date.now();
    const windowStart = now - 60000; // 1分钟窗口

    // 获取客户端的请求记录
    const clientRequests = requests.get(clientIP) || [];
    
    // 清理过期的请求记录
    const validRequests = clientRequests.filter(time => time > windowStart);
    
    // 检查是否超过限制
    if (validRequests.length >= requestsPerMinute) {
      return c.json(
        createOpenAIErrorResponse(
          "Rate limit exceeded",
          "rate_limit_exceeded"
        ),
        429
      );
    }

    // 记录当前请求
    validRequests.push(now);
    requests.set(clientIP, validRequests);

    // 定期清理过期数据
    if (Math.random() < 0.01) { // 1% 概率清理
      for (const [ip, times] of requests.entries()) {
        const validTimes = times.filter(time => time > windowStart);
        if (validTimes.length === 0) {
          requests.delete(ip);
        } else {
          requests.set(ip, validTimes);
        }
      }
    }

    await next();
  };
}

/**
 * 安全头中间件
 */
export async function securityHeaders(c: Context, next: Next) {
  await next();
  
  // 添加安全相关的响应头
  c.header("X-Content-Type-Options", "nosniff");
  c.header("X-Frame-Options", "DENY");
  c.header("X-XSS-Protection", "1; mode=block");
  c.header("Referrer-Policy", "strict-origin-when-cross-origin");
  
  // 移除可能泄露服务器信息的头
  c.header("Server", "");
}

/**
 * 请求日志中间件
 */
export function requestLogger() {
  return async (c: Context, next: Next) => {
    const start = Date.now();
    const method = c.req.method;
    const path = c.req.path;
    const userAgent = c.req.header("user-agent") || "";
    const requestId = c.get("requestId");

    console.log(`📥 [${requestId}] ${method} ${path} - Start`);

    await next();

    const duration = Date.now() - start;
    const status = c.res.status;
    
    const logLevel = status >= 500 ? "error" : status >= 400 ? "warn" : "info";
    const emoji = status >= 500 ? "❌" : status >= 400 ? "⚠️" : "✅";
    
    if (env.LOG_LEVEL === "debug" || 
        (env.LOG_LEVEL === "info" && logLevel !== "debug") ||
        (env.LOG_LEVEL === "warn" && (logLevel === "warn" || logLevel === "error")) ||
        (env.LOG_LEVEL === "error" && logLevel === "error")) {
      
      console.log(
        `📤 ${emoji} [${requestId}] ${method} ${path} - ${status} (${duration}ms) ${userAgent}`
      );
    }
  };
}

/**
 * 健康检查跳过日志中间件
 */
export function skipHealthCheckLogs() {
  return async (c: Context, next: Next) => {
    if (c.req.path === "/health") {
      // 跳过健康检查的详细日志
      await next();
    } else {
      await requestLogger()(c, next);
    }
  };
}

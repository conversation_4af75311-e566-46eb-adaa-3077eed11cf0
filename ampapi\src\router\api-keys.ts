import { createRouter } from "../lib/create-app.ts";
import {
  getApi<PERSON><PERSON><PERSON>,
  add<PERSON><PERSON><PERSON><PERSON>,
  delete<PERSON><PERSON><PERSON><PERSON>,
  resetApi<PERSON>eyUsage,
  getTotalRemainingUsage,
  getApiRequestStats,
} from "../lib/db.ts";
import { createOpenAIErrorResponse } from "../utils/format-converter.ts";
import { AppContext } from "../lib/types.ts";

const router = createRouter("/api-keys");

/**
 * 获取所有 API Keys
 */
router.get("/", async (c: AppContext) => {
  try {
    const apiKeys = await getApiKeys();
    const totalRemaining = await getTotalRemainingUsage();
    
    return c.json({
      success: true,
      data: {
        apiKeys: apiKeys.map(key => ({
          id: key.id,
          key: `${key.key.substring(0, 8)}...${key.key.substring(key.key.length - 4)}`, // 脱敏显示
          usage: key.usage,
          usageLimit: key.usageLimit,
          active: key.active,
          disabledAt: key.disabledAt,
          disabledReason: key.disabledReason,
          createdAt: key.createdAt,
          updatedAt: key.updatedAt,
        })),
        totalRemaining,
        summary: {
          total: apiKeys.length,
          active: apiKeys.filter(k => k.active).length,
          disabled: apiKeys.filter(k => !k.active).length,
        },
      },
    });
  } catch (error) {
    console.error("获取 API Keys 失败:", error);
    return c.json(
      createOpenAIErrorResponse("Failed to fetch API keys", "internal_error"),
      500
    );
  }
});

/**
 * 添加新的 API Key
 */
router.post("/", async (c: AppContext) => {
  try {
    const body = await c.req.json();
    const { key, usageLimit = 1000 } = body;

    if (!key || typeof key !== "string") {
      return c.json(
        createOpenAIErrorResponse("Missing or invalid API key", "invalid_request"),
        400
      );
    }

    if (typeof usageLimit !== "number" || usageLimit <= 0) {
      return c.json(
        createOpenAIErrorResponse("Invalid usage limit", "invalid_request"),
        400
      );
    }

    await addApiKey(key, usageLimit);

    return c.json({
      success: true,
      message: "API key added successfully",
    });
  } catch (error) {
    console.error("添加 API Key 失败:", error);
    
    // 检查是否是重复键错误
    if (error instanceof Error && error.message.includes("duplicate")) {
      return c.json(
        createOpenAIErrorResponse("API key already exists", "duplicate_key"),
        409
      );
    }

    return c.json(
      createOpenAIErrorResponse("Failed to add API key", "internal_error"),
      500
    );
  }
});

/**
 * 删除 API Key
 */
router.delete("/:key", async (c: AppContext) => {
  try {
    const key = c.req.param("key");

    if (!key) {
      return c.json(
        createOpenAIErrorResponse("Missing API key parameter", "invalid_request"),
        400
      );
    }

    await deleteApiKey(key);

    return c.json({
      success: true,
      message: "API key deleted successfully",
    });
  } catch (error) {
    console.error("删除 API Key 失败:", error);
    return c.json(
      createOpenAIErrorResponse("Failed to delete API key", "internal_error"),
      500
    );
  }
});

/**
 * 重置 API Key 使用量
 */
router.post("/:key/reset", async (c: AppContext) => {
  try {
    const key = c.req.param("key");

    if (!key) {
      return c.json(
        createOpenAIErrorResponse("Missing API key parameter", "invalid_request"),
        400
      );
    }

    await resetApiKeyUsage(key);

    return c.json({
      success: true,
      message: "API key usage reset successfully",
    });
  } catch (error) {
    console.error("重置 API Key 使用量失败:", error);
    return c.json(
      createOpenAIErrorResponse("Failed to reset API key usage", "internal_error"),
      500
    );
  }
});

/**
 * 获取 API 请求统计
 */
router.get("/stats", async (c: AppContext) => {
  try {
    const timeRange = c.req.query("range") as "1h" | "24h" | "7d" || "24h";
    
    const stats = await getApiRequestStats(timeRange);

    return c.json({
      success: true,
      data: {
        timeRange,
        totalRequests: Number(stats.total_requests || 0),
        successfulRequests: Number(stats.successful_requests || 0),
        failedRequests: Number(stats.failed_requests || 0),
        successRate: stats.total_requests > 0 
          ? Number(((stats.successful_requests / stats.total_requests) * 100).toFixed(2))
          : 0,
        averageResponseTime: stats.avg_response_time_ms 
          ? Number(Number(stats.avg_response_time_ms).toFixed(2))
          : null,
      },
    });
  } catch (error) {
    console.error("获取统计信息失败:", error);
    return c.json(
      createOpenAIErrorResponse("Failed to fetch statistics", "internal_error"),
      500
    );
  }
});

export default router;

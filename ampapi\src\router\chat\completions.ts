import { createRouter } from "../../lib/create-app.ts";
import { streamSSE } from "hono/streaming";
import {
  getAvailableApiKey,
  updateApiKeyStatus,
  recordApiRequest,
} from "../../lib/db.ts";
import {
  convertOpenAIToAnthropicRequest,
  convertAnthropicToOpenAIResponse,
  convertAnthropicStreamToOpenAI,
  validateOpenAIRequest,
  createOpenAIErrorResponse,
} from "../../utils/format-converter.ts";
import { ApiKeyDisabledReason, AppContext } from "../../lib/types.ts";
import { env } from "../../env.ts";

const router = createRouter("/v1/chat/completions");

/**
 * 聊天完成接口 - 兼容 OpenAI 格式
 */
router.post("/", async (c: AppContext) => {
  const requestId = c.get("requestId");
  const startTime = Date.now();

  try {
    // 1. 解析请求体
    const requestBody = await c.req.json();
    
    // 2. 验证请求格式
    const validation = validateOpenAIRequest(requestBody);
    if (!validation.valid) {
      return c.json(createOpenAIErrorResponse(validation.error!), 400);
    }

    // 3. 获取可用的 API Key
    const apiKey = await getAvailableApiKey();
    if (!apiKey) {
      return c.json(
        createOpenAIErrorResponse("No available API keys", "insufficient_quota"),
        503
      );
    }

    // 4. 转换请求格式
    const anthropicRequest = convertOpenAIToAnthropicRequest(requestBody);
    
    // 5. 判断是否为流式请求
    const isStreaming = requestBody.stream === true;

    if (isStreaming) {
      return handleStreamingRequest(c, anthropicRequest, apiKey.key, requestBody.model, requestId, startTime);
    } else {
      return handleNonStreamingRequest(c, anthropicRequest, apiKey.key, requestBody.model, requestId, startTime);
    }
  } catch (error) {
    console.error("处理请求时发生错误:", error);
    return c.json(
      createOpenAIErrorResponse("Internal server error", "internal_error"),
      500
    );
  }
});

/**
 * 处理非流式请求
 */
async function handleNonStreamingRequest(
  c: AppContext,
  anthropicRequest: any,
  apiKey: string,
  originalModel: string,
  requestId: string,
  startTime: number
) {
  try {
    // 发送请求到 Anthropic API
    const response = await fetch(`${env.ANTHROPIC_BASE_URL}/v1/messages`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": apiKey,
        "anthropic-version": "2023-06-01",
      },
      body: JSON.stringify(anthropicRequest),
    });

    const responseTime = Date.now();
    const responseTimeMs = responseTime - startTime;

    if (!response.ok) {
      return handleApiError(c, response, apiKey, requestId, responseTimeMs);
    }

    // 解析 Anthropic 响应
    const anthropicResponse = await response.json();
    
    // 转换为 OpenAI 格式
    const openaiResponse = convertAnthropicToOpenAIResponse(anthropicResponse, originalModel);

    // 记录成功请求
    await updateApiKeyStatus(apiKey, { incrementUsage: true });
    await recordApiRequest(apiKey, true, requestId, { responseTimeMs });

    return c.json(openaiResponse);
  } catch (error) {
    console.error("非流式请求处理失败:", error);
    const responseTimeMs = Date.now() - startTime;
    
    await recordApiRequest(apiKey, false, requestId, {
      errorType: "request_failed",
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      responseTimeMs,
    });

    return c.json(
      createOpenAIErrorResponse("Request failed", "request_failed"),
      500
    );
  }
}

/**
 * 处理流式请求
 */
async function handleStreamingRequest(
  c: AppContext,
  anthropicRequest: any,
  apiKey: string,
  originalModel: string,
  requestId: string,
  startTime: number
) {
  try {
    // 发送流式请求到 Anthropic API
    const response = await fetch(`${env.ANTHROPIC_BASE_URL}/v1/messages`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": apiKey,
        "anthropic-version": "2023-06-01",
      },
      body: JSON.stringify(anthropicRequest),
    });

    if (!response.ok) {
      const responseTimeMs = Date.now() - startTime;
      return handleApiError(c, response, apiKey, requestId, responseTimeMs);
    }

    if (!response.body) {
      const responseTimeMs = Date.now() - startTime;
      await recordApiRequest(apiKey, false, requestId, {
        errorType: "no_response_body",
        errorMessage: "No response body received",
        responseTimeMs,
      });
      return c.json(
        createOpenAIErrorResponse("No response body", "request_failed"),
        500
      );
    }

    // 返回流式响应
    return streamSSE(c, async (stream) => {
      const reader = response.body!.getReader();
      const decoder = new TextDecoder();
      let responseTimeMs = 0;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const convertedChunk = convertAnthropicStreamToOpenAI(chunk, originalModel);
          
          await stream.write(convertedChunk);
        }

        // 发送结束标记
        await stream.write("data: [DONE]\n\n");
        
        responseTimeMs = Date.now() - startTime;
        
        // 记录成功请求
        await updateApiKeyStatus(apiKey, { incrementUsage: true });
        await recordApiRequest(apiKey, true, requestId, { responseTimeMs });
      } catch (error) {
        console.error("流式响应处理失败:", error);
        responseTimeMs = Date.now() - startTime;
        
        await recordApiRequest(apiKey, false, requestId, {
          errorType: "stream_failed",
          errorMessage: error instanceof Error ? error.message : "Unknown error",
          responseTimeMs,
        });

        // 发送错误事件
        await stream.write(`event: error\ndata: ${JSON.stringify({
          message: "Stream processing failed",
        })}\n\n`);
      } finally {
        reader.releaseLock();
        stream.close();
      }
    });
  } catch (error) {
    console.error("流式请求初始化失败:", error);
    const responseTimeMs = Date.now() - startTime;
    
    await recordApiRequest(apiKey, false, requestId, {
      errorType: "stream_init_failed",
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      responseTimeMs,
    });

    return c.json(
      createOpenAIErrorResponse("Stream initialization failed", "request_failed"),
      500
    );
  }
}

/**
 * 处理 API 错误
 */
async function handleApiError(
  c: AppContext,
  response: Response,
  apiKey: string,
  requestId: string,
  responseTimeMs: number
) {
  const errorText = await response.text();
  console.error(`Anthropic API 请求失败: ${response.status} ${errorText}`);

  let errorType = "api_error";
  let shouldDisableKey = false;

  // 根据错误状态码决定是否禁用 API Key
  switch (response.status) {
    case 401:
      errorType = "authentication_error";
      shouldDisableKey = true;
      break;
    case 403:
      errorType = "permission_denied";
      shouldDisableKey = true;
      break;
    case 429:
      errorType = "rate_limit_exceeded";
      // 可以选择是否禁用，这里暂时不禁用
      break;
    case 500:
    case 502:
    case 503:
    case 504:
      errorType = "server_error";
      break;
    default:
      errorType = "unknown_error";
  }

  // 更新 API Key 状态
  if (shouldDisableKey) {
    await updateApiKeyStatus(apiKey, {
      disable: true,
      disabledReason: errorType as ApiKeyDisabledReason,
    });
  }

  // 记录失败请求
  await recordApiRequest(apiKey, false, requestId, {
    errorType,
    errorMessage: errorText,
    responseTimeMs,
  });

  // 返回适当的错误响应
  const statusCode = response.status >= 500 ? 502 : response.status;
  return c.json(
    createOpenAIErrorResponse(
      shouldDisableKey ? "API key authentication failed" : "API request failed",
      errorType
    ),
    statusCode
  );
}

export default router;

import {
  OpenAIRequest,
  OpenAIResponse,
  OpenAIStreamChunk,
  AnthropicRequest,
  AnthropicResponse,
  OpenAIMessage,
  AnthropicMessage,
} from "../lib/types.ts";

/**
 * 将 OpenAI 格式的请求转换为 Anthropic 格式
 */
export function convertOpenAIToAnthropicRequest(openaiRequest: OpenAIRequest): AnthropicRequest {
  const { messages, model, max_tokens = 4096, temperature, top_p, stream, stop } = openaiRequest;

  // 提取系统消息
  let systemMessage = "";
  const anthropicMessages: AnthropicMessage[] = [];

  for (const message of messages) {
    if (message.role === "system") {
      systemMessage += (systemMessage ? "\n\n" : "") + message.content;
    } else if (message.role === "user" || message.role === "assistant") {
      anthropicMessages.push({
        role: message.role,
        content: message.content,
      });
    }
  }

  const anthropicRequest: AnthropicRequest = {
    model: mapOpenAIModelToAnthropic(model),
    max_tokens,
    messages: anthropicMessages,
  };

  // 添加可选参数
  if (systemMessage) {
    anthropicRequest.system = systemMessage;
  }
  if (temperature !== undefined) {
    anthropicRequest.temperature = temperature;
  }
  if (top_p !== undefined) {
    anthropicRequest.top_p = top_p;
  }
  if (stream !== undefined) {
    anthropicRequest.stream = stream;
  }
  if (stop) {
    anthropicRequest.stop_sequences = Array.isArray(stop) ? stop : [stop];
  }

  return anthropicRequest;
}

/**
 * 将 Anthropic 格式的响应转换为 OpenAI 格式
 */
export function convertAnthropicToOpenAIResponse(
  anthropicResponse: AnthropicResponse,
  originalModel: string
): OpenAIResponse {
  const content = anthropicResponse.content
    .filter(item => item.type === "text")
    .map(item => item.text)
    .join("");

  return {
    id: anthropicResponse.id,
    object: "chat.completion",
    created: Math.floor(Date.now() / 1000),
    model: originalModel,
    choices: [
      {
        index: 0,
        message: {
          role: "assistant",
          content,
        },
        finish_reason: mapAnthropicStopReasonToOpenAI(anthropicResponse.stop_reason),
      },
    ],
    usage: {
      prompt_tokens: anthropicResponse.usage.input_tokens,
      completion_tokens: anthropicResponse.usage.output_tokens,
      total_tokens: anthropicResponse.usage.input_tokens + anthropicResponse.usage.output_tokens,
    },
  };
}

/**
 * 处理 Anthropic 流式响应并转换为 OpenAI 格式
 */
export function convertAnthropicStreamToOpenAI(
  chunk: string,
  originalModel: string
): string {
  const lines = chunk.split("\n");
  const convertedLines: string[] = [];

  for (const line of lines) {
    if (line.startsWith("data: ")) {
      const dataStr = line.substring(6).trim();
      
      if (dataStr === "" || dataStr === "[DONE]") {
        convertedLines.push(line);
        continue;
      }

      try {
        const data = JSON.parse(dataStr);
        
        if (data.type === "message_start") {
          // 消息开始
          const openaiChunk: OpenAIStreamChunk = {
            id: data.message.id,
            object: "chat.completion.chunk",
            created: Math.floor(Date.now() / 1000),
            model: originalModel,
            choices: [
              {
                index: 0,
                delta: { role: "assistant" },
                finish_reason: null,
              },
            ],
          };
          convertedLines.push(`data: ${JSON.stringify(openaiChunk)}`);
        } else if (data.type === "content_block_delta") {
          // 内容增量
          if (data.delta?.text) {
            const openaiChunk: OpenAIStreamChunk = {
              id: data.index || crypto.randomUUID(),
              object: "chat.completion.chunk",
              created: Math.floor(Date.now() / 1000),
              model: originalModel,
              choices: [
                {
                  index: 0,
                  delta: { content: data.delta.text },
                  finish_reason: null,
                },
              ],
            };
            convertedLines.push(`data: ${JSON.stringify(openaiChunk)}`);
          }
        } else if (data.type === "message_delta") {
          // 消息结束
          if (data.delta?.stop_reason) {
            const openaiChunk: OpenAIStreamChunk = {
              id: crypto.randomUUID(),
              object: "chat.completion.chunk",
              created: Math.floor(Date.now() / 1000),
              model: originalModel,
              choices: [
                {
                  index: 0,
                  delta: {},
                  finish_reason: mapAnthropicStopReasonToOpenAI(data.delta.stop_reason),
                },
              ],
            };
            convertedLines.push(`data: ${JSON.stringify(openaiChunk)}`);
          }
        } else {
          // 其他类型的数据，保持原样
          convertedLines.push(line);
        }
      } catch (error) {
        console.error("解析 Anthropic 流数据失败:", error, "原始数据:", dataStr);
        convertedLines.push(line);
      }
    } else {
      convertedLines.push(line);
    }
  }

  return convertedLines.join("\n");
}

/**
 * 映射 OpenAI 模型名称到 Anthropic 模型名称
 */
function mapOpenAIModelToAnthropic(openaiModel: string): string {
  const modelMap: Record<string, string> = {
    "gpt-4": "claude-3-opus-20240229",
    "gpt-4-turbo": "claude-3-sonnet-20240229",
    "gpt-3.5-turbo": "claude-3-haiku-20240307",
    "claude-3-opus": "claude-3-opus-20240229",
    "claude-3-sonnet": "claude-3-sonnet-20240229",
    "claude-3-haiku": "claude-3-haiku-20240307",
  };

  return modelMap[openaiModel] || openaiModel;
}

/**
 * 映射 Anthropic 停止原因到 OpenAI 格式
 */
function mapAnthropicStopReasonToOpenAI(
  stopReason: string | null
): "stop" | "length" | "content_filter" | null {
  switch (stopReason) {
    case "end_turn":
      return "stop";
    case "max_tokens":
      return "length";
    case "stop_sequence":
      return "stop";
    default:
      return null;
  }
}

/**
 * 生成符合 OpenAI 格式的错误响应
 */
export function createOpenAIErrorResponse(
  message: string,
  type: string = "invalid_request_error",
  code?: string
) {
  return {
    error: {
      message,
      type,
      code,
    },
  };
}

/**
 * 验证 OpenAI 请求格式
 */
export function validateOpenAIRequest(request: any): { valid: boolean; error?: string } {
  if (!request.model) {
    return { valid: false, error: "Missing required field: model" };
  }

  if (!request.messages || !Array.isArray(request.messages)) {
    return { valid: false, error: "Missing or invalid field: messages" };
  }

  if (request.messages.length === 0) {
    return { valid: false, error: "Messages array cannot be empty" };
  }

  for (const message of request.messages) {
    if (!message.role || !["system", "user", "assistant"].includes(message.role)) {
      return { valid: false, error: "Invalid message role" };
    }
    if (!message.content || typeof message.content !== "string") {
      return { valid: false, error: "Invalid message content" };
    }
  }

  return { valid: true };
}

#!/usr/bin/env -S deno run -A --env-file=.env

/**
 * 测试环境变量配置
 */

console.log("🔍 测试环境变量配置...");
console.log("");

// 显示所有相关的环境变量
const envVars = [
  "DATABASE_URL",
  "ANTHROPIC_BASE_URL", 
  "LOG_LEVEL",
  "PORT"
];

console.log("📋 环境变量列表:");
envVars.forEach(key => {
  const value = Deno.env.get(key);
  if (value) {
    // 脱敏显示敏感信息
    if (key === "DATABASE_URL") {
      const maskedValue = value.replace(/\/\/[^:]+:[^@]+@/, "//***:***@");
      console.log(`  ✅ ${key}: ${maskedValue}`);
    } else {
      console.log(`  ✅ ${key}: ${value}`);
    }
  } else {
    console.log(`  ❌ ${key}: 未设置`);
  }
});

console.log("");

// 测试环境变量解析
try {
  const { parseEnv } = await import("./src/env.ts");
  const env = parseEnv();
  console.log("✅ 环境变量解析成功!");
  console.log(`   数据库: ${env.DATABASE_URL.replace(/\/\/[^:]+:[^@]+@/, "//***:***@")}`);
  console.log(`   API 地址: ${env.ANTHROPIC_BASE_URL}`);
  console.log(`   日志级别: ${env.LOG_LEVEL}`);
  console.log(`   端口: ${env.PORT}`);
} catch (error) {
  console.error("❌ 环境变量解析失败:", error.message);
}

console.log("");

// 测试数据库连接
try {
  console.log("🔌 测试数据库连接...");
  const { neon } = await import("@neondatabase/serverless");
  const databaseUrl = Deno.env.get("DATABASE_URL");
  
  if (!databaseUrl) {
    throw new Error("DATABASE_URL 未设置");
  }
  
  const sql = neon(databaseUrl);
  const result = await sql`SELECT 1 as test`;
  
  if (result && result[0]?.test === 1) {
    console.log("✅ 数据库连接成功!");
  } else {
    console.log("⚠️ 数据库连接异常");
  }
} catch (error) {
  console.error("❌ 数据库连接失败:", error.message);
  console.log("💡 请确保:");
  console.log("   1. PostgreSQL 服务正在运行");
  console.log("   2. DATABASE_URL 配置正确");
  console.log("   3. 网络连接正常");
}

console.log("");
console.log("🎯 测试完成!");

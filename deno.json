{"imports": {"@neondatabase/serverless": "npm:@neondatabase/serverless@^1.0.0", "@upstash/redis": "npm:@upstash/redis@^1.34.9", "hono": "npm:hono@^4.7.10", "neverthrow": "npm:neverthrow@^8.2.0", "playwright": "npm:playwright@^1.52.0", "steel-sdk": "npm:steel-sdk@^0.3.0", "zod": "npm:zod@^3.25.28", "@/": "./src/"}, "tasks": {"dev": "deno run -A --unstable-kv --unstable-cron --watch src/app.ts"}, "compilerOptions": {"jsx": "precompile", "jsxImportSource": "hono/jsx"}}
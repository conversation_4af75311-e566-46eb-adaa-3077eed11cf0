import "./env-runtime.ts";
import "./utils/redis.ts";
import "./cron/check-remain.ts";
import { createApp } from "./lib/create-app.ts";
import { getApiKeys } from "./lib/db.ts";
import chatCompletionsRouter from "./router/v1/chat/completions/route.ts";

const app = createApp();

const routes = [chatCompletionsRouter] as const;


app.get("/", (c) => {
  return c.json({ message: "Hello, World!" });
});

app.get("/api-keys", async (c) => {
  const apiKeys = await getApiKeys();
  return c.json(apiKeys);
});

routes.forEach((route) => {
  app.route("/", route);
});

Deno.serve(app.fetch);

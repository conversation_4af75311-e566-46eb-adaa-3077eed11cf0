import { addApi<PERSON>ey, getTotalRemainingUsage } from "@/lib/db.ts";
import { HumktAPI } from "@/lib/humkt.ts";
import { env } from "@/env-runtime.ts";
import { registerAmp } from "../lib/amp-register.ts";

const api = new HumktAPI(env.HUMKT_API_KEY);

Deno.cron(
  "Check Remain and Register New Key",
  { minute: { every: 1 } },
  { backoffSchedule: [1000, 2000, 3000, 4000, 5000] },
  async () => {
    const totalRemainingUsage = await getTotalRemainingUsage();
    console.log("Total Remaining Usage:", totalRemainingUsage);

    if (totalRemainingUsage < 100) {
      // 下单购买
      const order = await api.buyProduct(355, 1);
      console.log("订单ID:", order.data.id);

      // 查询订单
      const orderInfo = await api.getOrderWithPolling(order.data.id);
      console.log("订单详情:", orderInfo.data);
      for (const item of orderInfo.data) {
        const [email, password] = item.split("|");
        const newKey = await registerAmp(email, password);
        console.log("New Key:", newKey);
        if (newKey.apiKey && newKey.apiKey.startsWith("sgamp_user_")) {
          await addApiKey(newKey.apiKey);
        } else {
          throw new Error("New Key is not valid");
        }
      }
    }
  }
);

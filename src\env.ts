import { z } from "zod";

const EnvType = z.object({
  LOG_LEVEL: z.enum(["debug", "info", "warn", "error"]),
  API_KEY: z.string(),

  DATABASE_URL: z.string(),

  UPSTASH_REDIS_REST_URL: z.string(),
  UPSTASH_REDIS_REST_TOKEN: z.string(),

  SELF_HOSTED_GATEWAY_URL: z.string(),
  PORTKEY_API_KEY: z.string(),
  PORTKEY_CUSTOM_HOST: z.string(),
  PORTKEY_PROVIDER: z.string(),

  HUMKT_API_KEY: z.string(),
});

export type Environment = z.infer<typeof EnvType>;

export function parseEnv(data: unknown) {
  const out = EnvType.safeParse(data);

  if (!out.success) {
    console.error(out.error.message);
    Deno.exit(1);
  }

  return out.data;
}

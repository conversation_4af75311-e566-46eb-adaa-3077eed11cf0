import { chromium } from "playwright";
import Steel from "steel-sdk";
import { fromProm<PERSON> } from "neverthrow";

const client = new Steel({
  steelAPIKey:
    "ste-你的steel api key",
});

interface AccountResult {
  username?: string;
  password?: string;
  apiKey?: string | null;
  tompLink?: string | null;
  token?: string | null;
  remainingTime?: number | null;
  error?: string;
}

async function processAccount(
  email: string,
  password_param: string
): Promise<AccountResult> {
  let session;
  let browser;

  try {
    // Create a session
    console.log("Creating Steel session...");
    session = await client.sessions.create();
    console.log(`Session created at ${session.sessionViewerUrl}`);

    // Connect Playwright to the session
    browser = await chromium.connectOverCDP(
      `wss://connect.steel.dev?apiKey=ste-你的steel api key&sessionId=${session.id}`
    );
    console.log("Connected to browser via Playwright");

    // Create page at existing context to ensure session is recorded
    const currentContext = browser.contexts()[0];
    const page = await currentContext?.pages()[0];

    // ============ Your Automations Go Here! =============
    const accountResult: AccountResult = {
      username: email,
      password: password_param,
    };

    // Example script - Navigate to Hacker News and extract the top 5 stories
    console.log("进入amp...");
    await page?.goto("https://ampcode.com/threads", {
      waitUntil: "networkidle",
    });

    if (!page) {
      return accountResult;
    }

    let result = await fromPromise(
      page
        .getByRole("link")
        .filter({ hasText: "Continue with Google" })
        .click(),
      (e) => e
    );

    if (result.isErr()) {
      console.error("Continue with Google 按钮不存在", result.error);
      accountResult.error = "Continue with Google 按钮不存在";
      return accountResult;
    }

    console.log("点击Continue with Google 按钮");

    result = await fromPromise(
      page.locator("input[type='email']").fill(email),
      (e) => e
    );

    if (result.isErr()) {
      console.error("Email 输入框不存在", result.error);
      accountResult.error = "Email 输入框不存在";
      return accountResult;
    }

    console.log("输入Email");

    result = await fromPromise(
      page.getByRole("button").filter({ hasText: "Next" }).click(),
      (e) => e
    );

    if (result.isErr()) {
      console.error("Next 按钮不存在", result.error);
      accountResult.error = "Next 按钮不存在";
      return accountResult;
    }

    console.log("点击Next 按钮");

    result = await fromPromise(
      page
        .locator("input[type='password'][name='Passwd']")
        .fill(password_param),
      (e) => e
    );

    if (result.isErr()) {
      console.error("Password 输入框不存在", result.error);
      accountResult.error = "Password 输入框不存在";
      return accountResult;
    }

    console.log("输入Password");

    result = await fromPromise(
      page.getByRole("button").filter({ hasText: "Next" }).click(),
      (e) => e
    );

    if (result.isErr()) {
      console.error("Next 按钮不存在", result.error);
      accountResult.error = "Next 按钮不存在";
      return accountResult;
    }

    console.log("点击Next 按钮");

    result = await fromPromise(
      page
        .locator("input[type='submit'][name='confirm'][id='confirm']")
        .click(),
      (e) => e
    );

    if (result.isErr()) {
      console.error("我了解 按钮不存在", result.error);
    }

    console.log("点击我了解 按钮");

    result = await fromPromise(
      page.getByRole("button").filter({ hasText: "Continue" }).click(),
      (e) => e
    );

    if (result.isErr()) {
      console.error("Continue 按钮不存在", result.error);
    }

    console.log("点击Continue 按钮");

    await page.waitForLoadState("networkidle");

    const content = await page.content();

    const regex = /[A-Z0-9]{32}/;
    const match = content.match(regex);
    if (match) {
      accountResult.apiKey = match[0];
    }

    if (match) {
      const tompLink = "https://tool-2fa.deno.dev/2fa/" + match[0];
      accountResult.tompLink = tompLink;

      let retries = 0;
      const MAX_RETRIES = 5; // Maximum number of retries
      const RETRY_DELAY_MS = 2000; // Delay between retries in milliseconds

      while (retries < MAX_RETRIES) {
        const response = await fetch(tompLink);
        const tompLinkJson = await response.json();

        if (
          tompLinkJson &&
          typeof tompLinkJson.token === "string" &&
          typeof tompLinkJson.remainingTime === "number"
        ) {
          if (tompLinkJson.remainingTime > 10) {
            accountResult.token = tompLinkJson.token;
            accountResult.remainingTime = tompLinkJson.remainingTime;
            break; // Exit loop if successful
          } else {
            console.warn(
              `Remaining time is ${tompLinkJson.remainingTime}s, retrying... (${
                retries + 1
              }/${MAX_RETRIES})`
            );
            accountResult.error = `Remaining time for token is ${tompLinkJson.remainingTime}s. Retrying.`;
          }
        } else {
          accountResult.error =
            "Failed to retrieve token or remainingTime from 2FA link.";
          console.error(
            "Failed to retrieve token or remainingTime from 2FA link. Retrying..."
          );
        }

        retries++;
        if (retries < MAX_RETRIES) {
          await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY_MS));
        } else {
          console.error("Max retries reached for fetching 2FA token.");
          accountResult.error =
            "Max retries reached for fetching 2FA token. Remaining time not greater than 10s.";
        }
      }
    }

    console.log("输入2FA token", accountResult.token);

    if (accountResult.token) {
      for (let i = 0; i < accountResult.token.length; i++) {
        const char = accountResult.token[i];
        result = await fromPromise(
          page
            .locator(`input[data-index='${i}']`)
            .fill(char),
          (e) => e
        );
        if (result.isErr()) {
          console.error(`Error filling OTP at index ${i}:`, result.error);
          accountResult.error = `Error filling OTP at index ${i}`;
          return accountResult;
        }
      }
    } else {
      console.error("2FA token is missing");
      accountResult.error = "2FA token is missing";
      return accountResult;
    }

    console.log("输入2FA token 完成");

    await page.waitForTimeout(5000);

    await page.waitForLoadState("networkidle");

    await page.goto("https://ampcode.com/settings", {
      waitUntil: "networkidle",
    });

    const content2 = await page.content();

    const regex2 = /sgamp_user_[A-Za-z0-9]+_[A-Fa-f0-9]+/;
    const match2 = content2.match(regex2);
    if (match2) {
      accountResult.apiKey = match2[0];
      console.log("API key", accountResult.apiKey);
    }

    return accountResult;
  } catch (error: any) {
    console.error("An error occurred:", error);
    return {
      username: email,
      password: password_param,
      error: error.message || "Unknown error",
    };
  } finally {
    // Clean up resources
    if (browser) {
      await browser.close();
      console.log("Browser closed");
    }

    if (session) {
      await client.sessions.release(session.id);
      console.log("Session released");
    }

    console.log("Done!");
  }
}

async function main() {

  const result = await processAccount(
    "@sitebbyy.us",
    "Phan9999"
  );

  console.log(result);
}

export { processAccount as  registerAmp}

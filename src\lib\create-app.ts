import type { Env } from "./types.ts";
import { requestId } from "hono/request-id";
import { Hono } from "hono";
import { logger } from 'hono/logger'
import withAuth from "@/middleware/with-auth.ts";

export function createRouter(basePath: string = "/") {
  return new Hono<Env>({
    strict: false,
  }).basePath(basePath);
}

export function createApp() {
  const app = createRouter();
  app.use(requestId());
  app.use("*", withAuth);
  app.use(logger())
  return app;
}

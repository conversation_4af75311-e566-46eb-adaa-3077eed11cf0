import { neon } from "@neondatabase/serverless";
import { env } from "@/env-runtime.ts";

export interface ApiKey {
  key: string; // API密钥值
  usage: number; // 使用次数
  usageLimit: number; // 使用次数限制
  active: boolean; // 是否激活
  disabledAt: number; // 禁用时间戳
  disabledReason: string; // 禁用原因
  createdAt: number; // 创建时间戳
}

export interface ApiRequestResult {
  apiKey: string; // 使用的API密钥
  success: boolean; // 请求是否成功
  errorType?: string; // 错误类型
  errorMessage?: string; // 错误信息
  timestamp: number; // 请求时间戳
  responseTime?: number; // 响应时间(毫秒)
  requestId?: string; // 请求ID
}

export enum ApiKeyDisabledReason {
  USAGE_LIMIT_EXCEEDED = "usage_limit_exceeded", // 超过使用次数限制
  AUTHENTICATION_ERROR = "authentication_error", // 认证错误
  MANUALLY_DISABLED = "manually_disabled", // 手动禁用
  EXPIRED = "expired", // 过期
  SECURITY_VIOLATION = "security_violation", // 安全违规
}

const databaseUrl = env.DATABASE_URL;
const sql = neon(databaseUrl);

// Create the books table and insert initial data if it doesn't exist
await sql`
  CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    key TEXT NOT NULL,
    usage INT NOT NULL,
    usage_limit INT NOT NULL,
    active BOOLEAN NOT NULL,
    disabled_at TIMESTAMP,
    disabled_reason TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
  );
`;

await sql`
  CREATE TABLE IF NOT EXISTS api_requests (
    id SERIAL PRIMARY KEY,
    api_key TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    error_type TEXT,
    error_message TEXT,
    request_time TIMESTAMP,
    response_time TIMESTAMP,
    request_id TEXT
  );
`;

// Check if the table is empty
const { count } = await sql`SELECT COUNT(*)::INT as count FROM api_keys`.then(
  (rows: any[]) => rows[0]
);

if (count === 0) {
  // The table is empty, insert the api_keys records
  await sql`
    INSERT INTO api_keys (key, usage, usage_limit, active, disabled_at, disabled_reason, created_at) VALUES
      ('sgamp_user_01', 0, 1000, true, null, null, CURRENT_TIMESTAMP),
      ('sgamp_user_01', 0, 1000, true, null, null, CURRENT_TIMESTAMP)
  `;
}

export const getApiKeys = async () => {
  const apiKeys = await sql`SELECT * FROM api_keys`;
  return apiKeys as ApiKey[];
};

export const getApiKey = async (key: string) => {
  const apiKey = await sql`SELECT * FROM api_keys WHERE key = ${key}`;
  if (apiKey.length > 0) {
    return apiKey[0] as ApiKey;
  }
  return null;
};

export const addApiKey = async (key: string) => {
  await sql`INSERT INTO api_keys (key, usage, usage_limit, active, disabled_at, disabled_reason, created_at) VALUES (${key}, 0, 1000, true, null, null, CURRENT_TIMESTAMP)`;
};

export const getAvailableApiKey = async () => {
  const apiKey =
    await sql`SELECT * FROM api_keys WHERE active = true AND usage < usage_limit ORDER BY usage ASC LIMIT 1`;
  if (apiKey.length > 0) {
    return apiKey[0] as ApiKey;
  }
  return null;
};

export const getTotalRemainingUsage = async () => {
  const result = await sql`
    SELECT SUM(usage_limit - usage) as total_remaining_usage
    FROM api_keys
    WHERE active = true
  `;
  if (result.length > 0 && result[0].total_remaining_usage !== null) {
    return Number(result[0].total_remaining_usage);
  }
  return 0;
};

export const updateApiKeyStatus = async (
  key: string,
  disabledAt?: number | null,
  disabledReason?: string | null
) => {
  if (
    disabledAt !== undefined &&
    disabledAt !== null &&
    disabledReason !== undefined &&
    disabledReason !== null
  ) {
    // Manual disable
    await sql`
      UPDATE api_keys
      SET active = false, disabled_at = TO_TIMESTAMP(${
        disabledAt / 1000
      }), disabled_reason = ${disabledReason}
      WHERE key = ${key}
    `;
  } else {
    const reasonExceeded = ApiKeyDisabledReason.USAGE_LIMIT_EXCEEDED;
    await sql`
      UPDATE api_keys
      SET
        usage = usage + 1,
        active = CASE
                   WHEN usage + 1 >= usage_limit THEN false
                   ELSE active
                 END,
        disabled_at = CASE
                        WHEN usage + 1 >= usage_limit THEN CURRENT_TIMESTAMP
                        ELSE disabled_at
                      END,
        disabled_reason = CASE
                            WHEN usage + 1 >= usage_limit THEN ${reasonExceeded}
                            ELSE disabled_reason
                          END
      WHERE key = ${key} AND active = true
    `;
  }
};

export const recordApiRequest = async (
  apiKey: string,
  success: boolean,
  errorType: string | null,
  errorMessage: string | null,
  requestTime: number | null,
  responseTime: number | null,
  requestId: string | null
) => {
  await sql`INSERT INTO api_requests (api_key, success, error_type, error_message, request_time, response_time, request_id) VALUES (${apiKey}, ${success}, ${errorType}, ${errorMessage}, ${  requestTime ? sql`TO_TIMESTAMP(${requestTime / 1000})` : null
}, ${  responseTime ? sql`TO_TIMESTAMP(${responseTime / 1000})` : null
}, ${requestId})`;
};

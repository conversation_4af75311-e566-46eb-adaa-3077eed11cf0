// HumktAPI 客户端封装
// API文档地址：https://www.humkt.com/api

// 响应基础类型
interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 余额查询响应类型
interface BalanceData {
  balance: string;
}

// 商品类型
interface Product {
  id: number;
  in_stock: number;
  name: string;
  price: string;
}

// 下单响应类型
interface OrderData {
  id: number;
  user_id: number;
  total_amount: number;
  qty: string;
  product_id: number;
  updated_at: string;
  created_at: string;
}

// API 客户端类
export class HumktAPI {
  private baseUrl = "https://www.humkt.com/api";
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  /**
   * 查询账户余额
   * @returns Promise<BaseResponse<BalanceData>>
   */
  async getBalance(): Promise<BaseResponse<BalanceData>> {
    const url = `${this.baseUrl}/get-balance?token=${this.token}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 获取商品列表
   * @returns Promise<BaseResponse<Product[]>>
   */
  async getGoods(): Promise<BaseResponse<Product[]>> {
    const url = `${this.baseUrl}/goods?token=${this.token}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 商品下单
   * @param id 商品ID
   * @param qty 购买数量
   * @returns Promise<BaseResponse<OrderData>>
   */
  async buyProduct(id: number, qty: number): Promise<BaseResponse<OrderData>> {
    const url = `${this.baseUrl}/buy?id=${id}&qty=${qty}&token=${this.token}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 查询订单信息
   * @param id 订单ID
   * @returns Promise<BaseResponse<string[]>>
   */
  async getOrder(id: number): Promise<BaseResponse<string[]>> {
    const url = `${this.baseUrl}/order?id=${id}&token=${this.token}`;
    console.log(url);

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 轮询查询订单信息，直到有结果或超时
   * @param id 订单ID
   * @param interval 轮询间隔（毫秒），默认1000
   * @param timeout 超时时间（毫秒），默认10000
   * @returns Promise<BaseResponse<string[]>>
   */
  async getOrderWithPolling(
    id: number,
    interval = 3000,
    timeout = 120000
  ): Promise<BaseResponse<string[]>> {
    const start = Date.now();
    while (true) {
      try {
        const result = await this.getOrder(id);
        console.log(result);
        // 只要能查到就返回
        if (result && result.code === 1 && result.data && result.data.length > 0) {
          return result;
        }
      } catch (e: any) {
        // 如果是 404，继续轮询；其他错误直接抛出
        if (!/404/.test(e.message)) throw e;
      }
      if (Date.now() - start > timeout) {
        throw new Error("查询超时");
      }
      await new Promise((resolve) => setTimeout(resolve, interval));
    }
  }

  /**
   * 设置新的token
   * @param token 新的认证token
   */
  setToken(token: string): void {
    this.token = token;
  }

  /**
   * 获取当前token
   * @returns 当前的认证token
   */
  getToken(): string {
    return this.token;
  }
}

// 导出类型供其他地方使用
export type { BaseResponse, BalanceData, Product, OrderData };

// 使用示例：
/*
const api = new HumktAPI('your-token-here');

// 查询余额
const balance = await api.getBalance();
console.log('余额:', balance.data.balance);

// 获取商品列表
const goods = await api.getGoods();
console.log('商品数量:', goods.data.length);

// 下单购买
const order = await api.buyProduct(1, 1);
console.log('订单ID:', order.data.product_id);

// 查询订单
const orderInfo = await api.getOrder(order.data.product_id);
console.log('订单详情:', orderInfo.data);
*/

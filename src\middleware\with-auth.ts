import type { MiddlewareHandler } from "hono";
import { Env } from "@/lib/types.ts";
import { env } from "@/env-runtime.ts";

const withAuth: MiddlewareHandler<Env> = async (c, next) => {
  let apiKey = c.req.header("Authorization");
  apiKey = apiKey?.replace("Bearer ", "");

  if (!apiKey || apiKey !== env.API_KEY) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  return await next();
};

export default withAuth;

import { createRouter } from "@/lib/create-app.ts";
import { Context } from "hono";
import {
  ApiKeyDisabledReason,
  getAvailableApiKey,
  recordApiRequest,
  updateApiKeyStatus,
} from "@/lib/db.ts";
import { SSEStreamingApi, streamSSE } from "hono/streaming";
import { createPortkeyRequestOptions } from "@/utils/porket.ts";
import { env } from "@/env-runtime.ts";
import { ContentfulStatusCode } from "hono/utils/http-status";
import CONFIG from "@/lib/constants.ts";
import {
  convertAnthropicNonStreamResponseToOpenAISSEChunk,
  convertAnthropicSSEChunkToOpenAISSEChunk,
} from "@/utils/anthropic2openai.ts";
import { AppContext } from "@/lib/types.ts";

const router = createRouter("/v1/chat/completions");

router.post("/", async (c) => {
  const requestBody = await c.req.json();

  // 设置默认max_tokens（如果未提供）
  if (!("max_tokens" in requestBody)) {
    requestBody.max_tokens = CONFIG.DEFAULT_MAX_TOKENS;
  }

  const isStreaming = requestBody.stream === true;

  if (isStreaming) {
    // 处理流式请求
    return handleCompletionStream(c, requestBody);
  } else {
    // 处理非流式请求
    const response = await handleCompletion(c, requestBody);

    // 如果响应成功，应用转换
    if (response.status === 200) {
      const responseData = await response.json();
      const transformedData =
        convertAnthropicNonStreamResponseToOpenAISSEChunk(responseData);
      return c.json(transformedData);
    }

    return response;
  }
});

const handleError = async (
  c: Context,
  response: Response,
  selectedApiKey: string,
  requestTime: number,
  responseTime: number
) => {
  const errorText = await response.text();
  console.error(`请求失败: ${response.status} ${errorText}`);

  // 检查是否为认证错误
  if (response.status === 401) {
    await updateApiKeyStatus(
      selectedApiKey,
      Date.now(),
      ApiKeyDisabledReason.AUTHENTICATION_ERROR
    );
    await recordApiRequest(
      selectedApiKey,
      false,
      ApiKeyDisabledReason.AUTHENTICATION_ERROR,
      errorText,
      requestTime,
      responseTime,
      c.get("requestId")
    );
  } else {
    await updateApiKeyStatus(selectedApiKey, null, null);
    await recordApiRequest(
      selectedApiKey,
      false,
      null,
      errorText,
      requestTime,
      responseTime,
      c.get("requestId")
    );
  }

  return c.text("接口错误，请重试", response.status as ContentfulStatusCode);
};

const handleCompletion = async (c: AppContext, body: any) => {
  // 1. 获取API密钥
  const apiKey = await getAvailableApiKey();
  if (!apiKey) {
    return c.json(
      {
        error: {
          message: "No available API key",
        },
      },
      500
    );
  }
  // 2. 创建请求选项
  const requestOptions = createPortkeyRequestOptions(apiKey.key, body, "POST");

  // 3. 发送请求
  const requestTime = Date.now();
  const response = await fetch(
    `${env.SELF_HOSTED_GATEWAY_URL}/v1/chat/completions`,
    requestOptions
  );
  const responseTime = Date.now();

  // 4. 记录请求
  if (!response.ok) {
    return handleError(c, response, apiKey.key, requestTime, responseTime);
  }

  await updateApiKeyStatus(apiKey.key, null, null);

  await recordApiRequest(
    apiKey.key,
    true,
    null,
    null,
    requestTime,
    responseTime,
    c.get("requestId")
  );

  return response;
};

const handleCompletionStream = async (c: AppContext, body: any) => {
  // 1. 获取API密钥
  const apiKey = await getAvailableApiKey();
  if (!apiKey) {
    return c.json(
      {
        error: {
          message: "No available API key",
        },
      },
      500
    );
  }

  // 2. 创建请求选项
  const requestOptions = createPortkeyRequestOptions(apiKey.key, body, "POST");

  // 3. 发送请求
  const requestTime = Date.now();
  const response = await fetch(
    `${env.SELF_HOSTED_GATEWAY_URL}/v1/chat/completions`,
    requestOptions
  );

  let responseTime = Date.now();

  // 4. 记录请求
  if (!response.ok) {
    return handleError(c, response, apiKey.key, requestTime, responseTime);
  }

  if (!response.body) {
    return handleError(c, response, apiKey.key, requestTime, responseTime);
  }

  return streamSSE(c, async (stream: SSEStreamingApi) => {
    const reader = response.body!.getReader();
    const decoder = new TextDecoder();
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        const transformedChunk =
          convertAnthropicSSEChunkToOpenAISSEChunk(chunk);
        await stream.write(transformedChunk);
      }
    } catch (error) {
      console.error("读取Gateway流失败", error);
      // 向客户端流写入错误事件
      await stream.write(
        `event: error\ndata: ${JSON.stringify({
          message: "Error streaming data",
        })}\n\n`
      );
    } finally {
      reader.releaseLock();
      stream.close();
      await updateApiKeyStatus(apiKey.key, null, null);
      responseTime = Date.now();
      await recordApiRequest(
        apiKey.key,
        true,
        null,
        null,
        requestTime,
        responseTime,
        c.get("requestId")
      );
    }
  });
};

export default router;

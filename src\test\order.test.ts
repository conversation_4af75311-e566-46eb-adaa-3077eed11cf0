import { HumktAPI } from "@/lib/humkt.ts";
import { env } from "@/env-runtime.ts";

const api = new HumktAPI(env.HUMKT_API_KEY);

// 查询余额
const balance = await api.getBalance();
console.log('余额:', balance.data.balance);

// 获取商品列表
const goods = await api.getGoods();
console.log('商品数量:', goods.data.length);

// 下单购买
const order = await api.buyProduct(355, 1);
console.log('订单ID:', order.data.id);

// 查询订单
const orderInfo = await api.getOrderWithPolling(order.data.id, 1, 60000);
console.log('订单详情:', orderInfo.data);

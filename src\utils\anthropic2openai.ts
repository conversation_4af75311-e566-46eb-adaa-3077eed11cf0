export function convertAnthropicSSEChunkToOpenAISSEChunk(chunk: string): string {
  const lines = chunk.split("\n");
  const resultLines: string[] = [];

  for (const line of lines) {
    if (line.startsWith("data: ")) {
      const dataStr = line.substring(6); // Remove 'data: ' prefix

      if (dataStr.trim() === "" || dataStr.trim() === "[DONE]") {
        resultLines.push(line);
        continue;
      }

      try {
        const dataObj: any = JSON.parse(dataStr);

        if (
          dataObj.choices && dataObj.choices[0] && dataObj.choices[0].delta
        ) {
          const delta: any = dataObj.choices[0].delta;

          if (delta.content_blocks && Array.isArray(delta.content_blocks)) {
            for (const block of delta.content_blocks) {
              if (block.delta && block.delta.thinking !== undefined) {
                if (!delta.reasoning_content) {
                  delta.reasoning_content = "";
                }
                delta.reasoning_content += block.delta.thinking;
                delete block.delta.thinking;
                if (Object.keys(block.delta).length === 0) {
                  block.delta = {}; // 保证即使为空也是对象
                }
              }
            }
            delta.content_blocks = delta.content_blocks.filter((block: any) =>
              block.delta && Object.keys(block.delta).length > 0
            );
            if (delta.content_blocks.length === 0) {
              delete delta.content_blocks;
            }
          }
        }
        resultLines.push("data: " + JSON.stringify(dataObj));
      } catch (error) {
        console.error("解析SSE JSON失败:", error, "原始数据:", dataStr);
        resultLines.push(line); // 解析失败则保留原始行
      }
    } else {
      resultLines.push(line);
    }
  }
  return resultLines.join("\n");
}

export function convertAnthropicNonStreamResponseToOpenAISSEChunk(response: any): any {
  if (
    !response.choices || !response.choices[0] ||
    !response.choices[0].message
  ) {
    return response; // 结构不匹配，直接返回
  }

  const message = response.choices[0].message;

  if (message.content_blocks && Array.isArray(message.content_blocks)) {
    let reasoningContent = "";
    const filteredContentBlocks = [];

    for (const block of message.content_blocks) {
      if (block.thinking !== undefined) {
        reasoningContent += block.thinking;
        // 不直接删除thinking，而是创建一个不包含thinking的新block，如果block还有其他内容
        const { thinking, ...restBlock } = block;
        if (Object.keys(restBlock).length > 0) {
           filteredContentBlocks.push(restBlock);
        }
      } else {
        filteredContentBlocks.push(block);
      }
    }

    if (filteredContentBlocks.length > 0) {
      message.content_blocks = filteredContentBlocks;
    } else {
      delete message.content_blocks;
    }

    if (reasoningContent) {
      message.reasoning_content = reasoningContent;
    }
  }

  return response;
}
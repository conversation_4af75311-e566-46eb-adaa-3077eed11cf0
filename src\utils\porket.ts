import { env } from "../env-runtime.ts";
import CONF<PERSON> from "../lib/constants.ts";

export function createPortkeyRequestOptions(selectedApiKey: string, body?: any, method: string = "POST"): RequestInit {
  const requestOptions: RequestInit = {
    method,
    headers: {
      "Content-Type": "application/json",
      "x-portkey-api-key": env.PORTKEY_API_KEY,
      "x-portkey-custom-host": env.PORTKEY_CUSTOM_HOST,
      "x-portkey-strict-open-ai-compliance": "false",
      "x-portkey-provider": env.PORTKEY_PROVIDER,
      "x-portkey-config": JSON.stringify(CONFIG.PORTKEY_CONFIG),
      Authorization: `Bearer ${selectedApiKey}`,
    },
  };

  if (body) {
    requestOptions.body = JSON.stringify(body);
  }

  return requestOptions;
}